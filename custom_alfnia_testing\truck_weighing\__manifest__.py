{
    'name': 'Truck Weighing System',
    'version': '********.0',
    'category': 'Manufacturing',
    'summary': 'Truck Entry/Exit Weighing System with Scale Integration',
    'description': """
Truck Weighing System
=====================

Complete truck weighing solution for tracking cargo loading/unloading:

Features:
---------
* Truck entry and exit weighing
* Automatic weight capture from T-Scale hardware
* Net weight calculation (loading/unloading)
* Arabic thermal receipt printing (80mm)
* Vehicle type management
* Entry/exit permit tracking
* Weight discount/adjustment support
* Full-screen kiosk mode interface
* Integration with existing scale_integration module

Workflow:
---------
1. Truck enters - capture entry weight
2. Loading/unloading operations
3. Truck exits - capture exit weight
4. Calculate net cargo weight
5. Print thermal receipt

Receipt includes:
-----------------
* Receipt number and date
* Driver and vehicle information
* Entry/exit times
* Previous and current weights
* Net weight calculation
* Permit numbers
* Discount adjustments
    """,
    'author': 'Eithar Live',
    'website': 'https://www.eitharlive.com',
    'depends': [
        'base',
        'web',
        'mail',  # For chatter functionality
        'purchase',  # For purchase order integration
        'stock',  # For goods receipt
        'scale_integration',  # Our existing scale module
    ],
    'data': [
        # Security
        'security/truck_weighing_security.xml',
        'security/ir.model.access.csv',
        
        # Data
        'data/vehicle_type_data.xml',
        'data/material_type_data.xml',
        'data/sequence_data.xml',

        # Reports (load before views)
        'reports/truck_weighing_receipt_template.xml',
        'reports/truck_weighing_reports.xml',

        # Actions (load before menu)
        'views/truck_weighing_actions.xml',

        # Menu (load after actions)
        'views/truck_weighing_menu.xml',

        # Views
        'views/vehicle_type_views.xml',
        'views/material_type_views.xml',
        'views/truck_weighing_views.xml',
    ],

    'demo': [
        'demo/truck_weighing_demo.xml',
    ],
    'license': 'LGPL-3',
    'installable': True,
    'auto_install': False,
    'application': True,  # Standalone app
    'sequence': 10,
}
