# Picking Weight Capture Module

## Overview
This module provides weight capture functionality for delivery/outgoing picking operations in Odoo 17. It integrates with the existing `scale_integration` module to capture weights directly from connected scales and automatically update the `quantity_done` field for individual stock move lines.

## Features

### ✅ Weight Capture per Line
- **Individual Line Capture**: Each stock move line in outgoing pickings has its own "Capture Weight" button (📏)
- **Real-time Scale Integration**: Uses the same scale communication pattern as the `truck_weighing` module
- **Automatic Quantity Update**: Captured weight automatically updates the `quantity_done` field

### ✅ Data Protection
- **Readonly After Capture**: Once weight is captured, the `quantity_done` field becomes readonly
- **Prevent Manual Edits**: Users cannot manually edit quantities after weight capture
- **Audit Trail**: Stores capture timestamp and weight value for tracking

### ✅ Smart Visibility
- **Outgoing Only**: Weight capture buttons only appear for outgoing/delivery pickings
- **State-Aware**: Buttons hidden for completed or cancelled operations
- **Status Indicators**: Clear visual indicators for captured vs non-captured lines

## Dependencies
- `stock` (Odoo core inventory module)
- `scale_integration` (must be installed and configured)

## Installation

1. **Copy Module**: Place the `picking_weight_capture` folder in your Odoo addons directory
2. **Update Apps List**: Go to Apps → Update Apps List
3. **Install Module**: Search for "Picking Weight Capture" and click Install
4. **Configure Scale**: Ensure `scale_integration` module is properly configured with your scale

## Usage

### For Outgoing Pickings:
1. Open any outgoing/delivery picking operation
2. In the "Operations" tab, you'll see stock move lines
3. Click the "📏 Capture Weight" button on any line
4. Weight will be automatically captured from the scale
5. The `quantity_done` field will be updated with the captured weight
6. The field becomes readonly to prevent manual changes

### Fields Added:
- **Weight Captured**: Boolean flag indicating if weight was captured
- **Captured Weight**: The actual weight value captured from scale
- **Weight Capture Time**: Timestamp when weight was captured

## Technical Details

### Model Extensions:
- **stock.move.line**: Extended with weight capture functionality

### Key Methods:
- `action_capture_weight()`: Main method that captures weight from scale
- `_compute_show_capture_weight_button()`: Controls button visibility
- Custom `write()` method: Prevents manual quantity editing after capture

### Integration Pattern:
Uses the same scale integration pattern as `truck_weighing`:
```python
scale = self.env['scale.integration'].search([('gateway_connected', '=', True)], limit=1)
weight_data = scale.get_weight()
```

## Security
- Stock users can capture weights and view captured data
- Stock managers have full access including deletion rights
- Readonly enforcement prevents unauthorized quantity changes

## Error Handling
- Validates scale connection before capture
- Prevents duplicate captures on same line
- Clear error messages for common issues
- Logging for troubleshooting

## Compatibility
- **Odoo Version**: 17.0
- **Dependencies**: Requires `scale_integration` module
- **Scope**: Only affects outgoing/delivery picking operations
- **Integration**: Seamlessly works with existing stock workflows
