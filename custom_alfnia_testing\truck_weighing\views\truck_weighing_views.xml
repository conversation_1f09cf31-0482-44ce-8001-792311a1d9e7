<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Truck Weighing Tree View -->
        <record id="view_truck_weighing_tree" model="ir.ui.view">
            <field name="name">truck.weighing.tree</field>
            <field name="model">truck.weighing</field>
            <field name="arch" type="xml">
                <tree string="وزن الشاحنات" decoration-success="state == 'exit'"
                      decoration-info="state == 'entry'" decoration-muted="state == 'cancelled'">
                    <field name="receipt_number"/>
                    <field name="weighing_date"/>
                    <field name="driver_name"/>
                    <field name="supplier_id"/>
                    <field name="product_id"/>
                    <field name="vehicle_type_id"/>
                    <field name="plate_number"/>
                    <field name="weighing_type"/>
                    <field name="entry_weight"/>
                    <field name="exit_weight"/>
                    <field name="net_weight_tons"/>
                    <field name="purchase_order_id"/>
                    <field name="state" widget="badge"
                           decoration-success="state == 'exit'"
                           decoration-info="state == 'entry'"
                           decoration-muted="state == 'cancelled'"/>
                </tree>
            </field>
        </record>

        <!-- Truck Weighing Form View -->
        <record id="view_truck_weighing_form" model="ir.ui.view">
            <field name="name">truck.weighing.form</field>
            <field name="model">truck.weighing</field>
            <field name="arch" type="xml">
                <form string="وزن الشاحنات">
                    <!-- Hidden fields for button conditions -->
                    <field name="entry_weight" invisible="1"/>
                    <field name="exit_weight" invisible="1"/>

                    <header>
                        <button name="action_capture_entry_weight" type="object"
                                string="📏 التقاط وزن الدخول" class="oe_highlight"
                                invisible="state != 'entry' or entry_weight > 0"/>

                        <button name="action_capture_exit_weight" type="object"
                                string="📏 التقاط وزن الخروج" class="oe_highlight"
                                invisible="state != 'entry' or not entry_weight"/>

                        <button name="action_complete_exit" type="object"
                                string="✅ إكمال الخروج" class="oe_highlight"
                                invisible="state != 'entry' or not exit_weight"/>

                        <button name="action_print_receipt" type="object"
                                string="🖨️ طباعة الإيصال" class="btn-primary"
                                invisible="state != 'exit'"/>

                        <field name="state" widget="statusbar" statusbar_visible="entry,exit"/>
                    </header>
                    
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="receipt_number" readonly="1"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group string="معلومات المعاملة">
                                <field name="weighing_date"/>
                                <field name="weighing_type"/>
                                <field name="company_id" groups="base.group_multi_company"/>
                            </group>
                            <group string="الحالة">
                                <field name="entry_time"/>
                                <field name="exit_time" readonly="state == 'entry'"/>
                            </group>
                        </group>
                        
                        <group string="معلومات السائق والمركبة">
                            <group>
                                <field name="driver_name"/>
                                <field name="vehicle_type_id"/>
                                <field name="plate_number"/>
                            </group>
                            <group>
                                <field name="trailer_number"/>
                                <field name="material_type_ids" widget="many2many_tags"/>
                            </group>
                        </group>

                        <group string="معلومات أمر الشراء">
                            <group>
                                <field name="supplier_id" options="{'no_create': True}"/>
                                <field name="product_id" options="{'no_create': True}"/>
                            </group>
                            <group>
                                <field name="purchase_order_id" readonly="1"/>
                            </group>
                        </group>

                        <group string="معلومات الوزن">
                            <group>
                                <field name="entry_weight" widget="float"
                                       decoration-bf="1"/>
                            </group>
                            <group>
                                <field name="exit_weight" widget="float"
                                       decoration-bf="1"/>
                            </group>
                        </group>

                        <group string="الحسابات">
                            <group>
                                <field name="discount" widget="float"/>
                                <field name="net_weight" widget="float"
                                       decoration-bf="1" readonly="1"/>
                            </group>
                            <group>
                                <field name="entry_weight_tons" widget="float" readonly="1"/>
                                <field name="exit_weight_tons" widget="float" readonly="1"/>
                                <field name="net_weight_tons" widget="float"
                                       decoration-bf="1" readonly="1"/>
                            </group>
                        </group>

                        <group string="معلومات التصريح">
                            <group>
                                <field name="entry_permit_number"/>
                            </group>
                            <group>
                                <field name="exit_permit_number"/>
                            </group>
                        </group>

                        <group string="معلومات إضافية">
                            <field name="notes" nolabel="1"/>
                        </group>
                    </sheet>
                    
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>

        <!-- Truck Weighing Search View -->
        <record id="view_truck_weighing_search" model="ir.ui.view">
            <field name="name">truck.weighing.search</field>
            <field name="model">truck.weighing</field>
            <field name="arch" type="xml">
                <search string="وزن الشاحنات">
                    <field name="receipt_number"/>
                    <field name="driver_name"/>
                    <field name="vehicle_type_id"/>
                    <field name="plate_number"/>
                    <field name="trailer_number"/>

                    <filter string="دخول" name="entry" domain="[('state', '=', 'entry')]"/>
                    <filter string="خروج مكتمل" name="exit" domain="[('state', '=', 'exit')]"/>
                    <filter string="ملغي" name="cancelled" domain="[('state', '=', 'cancelled')]"/>

                    <separator/>
                    <filter string="تحميل" name="loading" domain="[('weighing_type', '=', 'loading')]"/>
                    <filter string="تفريغ" name="unloading" domain="[('weighing_type', '=', 'unloading')]"/>

                    <separator/>
                    <filter string="تاريخ الوزن" name="weighing_date" date="weighing_date"/>

                    <group expand="0" string="تجميع حسب">
                        <filter string="الحالة" name="group_state" context="{'group_by': 'state'}"/>
                        <filter string="نوع العملية" name="group_type" context="{'group_by': 'weighing_type'}"/>
                        <filter string="نوع المركبة" name="group_vehicle" context="{'group_by': 'vehicle_type_id'}"/>
                        <filter string="التاريخ" name="group_date" context="{'group_by': 'weighing_date'}"/>
                    </group>
                </search>
            </field>
        </record>



    </data>
</odoo>
