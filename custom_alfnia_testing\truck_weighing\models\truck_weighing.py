# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
from datetime import datetime, timedelta
import logging

_logger = logging.getLogger(__name__)


class TruckWeighing(models.Model):
    _name = 'truck.weighing'
    _description = 'Truck Weighing Transaction'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'receipt_number desc, create_date desc'
    _rec_name = 'receipt_number'

    # Header Information
    receipt_number = fields.Char(
        string='Receipt Number',
        required=True,
        copy=False,
        readonly=True,
        default=lambda self: _('New'),
        help='Unique receipt number (رقم الايصال)'
    )
    
    weighing_date = fields.Date(
        string='Weighing Date',
        required=True,
        default=fields.Date.context_today,
        help='Date of weighing transaction (التاريخ)'
    )
    
    weighing_type = fields.Selection([
        ('loading', 'تحميل'),
        ('unloading', 'تفريغ'),
    ], string='Operation Type', required=True, default='unloading',
       help='Type of operation - loading or unloading (النوع)')
    
    # Driver and Vehicle Information
    driver_name = fields.Char(
        string='Driver Name',
        required=True,
        help='Name of the truck driver (اسم السائق)'
    )
    
    vehicle_type_id = fields.Many2one(
        'vehicle.type',
        string='Vehicle Type',
        required=True,
        help='Type of vehicle (نوع المركبة)'
    )

    plate_number = fields.Char(
        string='Plate Number',
        required=True,
        help='Main vehicle plate number (رقم اللوحة)'
    )

    trailer_number = fields.Char(
        string='Trailer Number',
        help='Trailer number if applicable (رقم الجرار)'
    )

    material_type_ids = fields.Many2many(
        'material.type',
        string='Material Types',
        help='Types of materials being loaded/unloaded (أنواع المواد)'
    )

    # Purchase Order Information
    supplier_id = fields.Many2one(
        'res.partner',
        string='Supplier',
        domain=[('is_company', '=', True), ('supplier_rank', '>', 0)],
        help='Supplier for aluminum garbage'
    )

    product_id = fields.Many2one(
        'product.product',
        string='Product',
        help='Product being purchased (aluminum garbage)'
    )

    purchase_order_id = fields.Many2one(
        'purchase.order',
        string='Purchase Order',
        readonly=True,
        help='Generated purchase order'
    )
    
    # Timing Information
    entry_time = fields.Datetime(
        string='Entry Time',
        required=True,
        default=fields.Datetime.now,
        help='Time when truck entered (ساعة الدخول)'
    )
    
    exit_time = fields.Datetime(
        string='Exit Time',
        help='Time when truck exited (ساعة الخروج)'
    )
    
    # Weight Information - Visible to all, editable only by managers
    entry_weight = fields.Float(
        string='وزن الدخول (كغ)',
        digits=(10, 2),
        help='Weight when truck entered (وزن الدخول)',
        groups='truck_weighing.group_truck_weighing_user,truck_weighing.group_truck_weighing_manager'
    )

    exit_weight = fields.Float(
        string='وزن الخروج (كغ)',
        digits=(10, 2),
        help='Weight when truck exits (وزن الخروج)',
        groups='truck_weighing.group_truck_weighing_user,truck_weighing.group_truck_weighing_manager'
    )
    
    discount = fields.Float(
        string='Discount (kg)',
        digits=(10, 2),
        default=0.0,
        help='Weight discount for balancing (إجمالي الخصم)'
    )
    
    net_weight = fields.Float(
        string='الوزن الصافي (كغ)',
        digits=(10, 2),
        default=0.0,
        help='Calculated net weight (الوزن الصافي)'
    )

    # Weight in tons (computed fields)
    entry_weight_tons = fields.Float(
        string='Entry Weight (Tons)',
        compute='_compute_weight_tons',
        digits=(10, 3),
        help='Entry weight converted to tons'
    )

    exit_weight_tons = fields.Float(
        string='Exit Weight (Tons)',
        compute='_compute_weight_tons',
        digits=(10, 3),
        help='Exit weight converted to tons'
    )

    net_weight_tons = fields.Float(
        string='Net Weight (Tons)',
        compute='_compute_weight_tons',
        digits=(10, 3),
        help='Net weight converted to tons'
    )
    
    # Permit Information
    entry_permit_number = fields.Char(
        string='Entry Permit Number',
        help='Entry permit number (رقم تصريح الدخول)'
    )
    
    exit_permit_number = fields.Char(
        string='Exit Permit Number',
        help='Exit permit number (رقم تصريح الخروج)'
    )
    
    # Status and Control
    state = fields.Selection([
        ('entry', 'تم تسجيل الدخول'),
        ('exit', 'تم إكمال الخروج'),
        ('cancelled', 'ملغي'),
    ], string='Status', default='entry', tracking=True)
    
    # Additional Information
    notes = fields.Text(
        string='Notes',
        help='Additional notes or comments'
    )
    
    company_id = fields.Many2one(
        'res.company',
        string='Company',
        default=lambda self: self.env.company,
        required=True
    )

    # Helper field for access control
    is_manager = fields.Boolean(
        string='Is Manager',
        compute='_compute_is_manager',
        help='Check if current user is a manager'
    )
    
    def _calculate_net_weight(self):
        """Calculate net weight based on entry, exit weights and discount"""
        if self.entry_weight and self.exit_weight:
            return abs(self.entry_weight - self.exit_weight) - (self.discount or 0)
        return 0.0

    def _compute_is_manager(self):
        """Check if current user is a manager"""
        for record in self:
            record.is_manager = self.env.user.has_group('truck_weighing.group_truck_weighing_manager')

    @api.depends('entry_weight', 'exit_weight', 'net_weight')
    def _compute_weight_tons(self):
        """Convert weights from kg to tons"""
        for record in self:
            record.entry_weight_tons = (record.entry_weight or 0.0) / 1000.0
            record.exit_weight_tons = (record.exit_weight or 0.0) / 1000.0
            record.net_weight_tons = (record.net_weight or 0.0) / 1000.0

    @api.onchange('entry_weight', 'exit_weight', 'discount')
    def _onchange_weights(self):
        """Recalculate net weight when weights change"""
        if self.entry_weight and self.exit_weight:
            self.net_weight = abs(self.entry_weight - self.exit_weight) - (self.discount or 0)
        else:
            self.net_weight = 0.0
    

    
    def action_capture_entry_weight(self):
        """Capture weight from scale for entry"""
        self.ensure_one()

        # Validate required fields for PO creation
        if not self.supplier_id:
            raise UserError(_('Please select a supplier before capturing entry weight.'))
        if not self.product_id:
            raise UserError(_('Please select a product before capturing entry weight.'))

        try:
            # Get the first available scale integration
            scale = self.env['scale.integration'].search([('gateway_connected', '=', True)], limit=1)
            if not scale:
                scale = self.env['scale.integration'].search([], limit=1)

            if not scale:
                raise UserError(_('No scale configuration found. Please configure scale integration first.'))

            # Get weight from scale
            weight_data = scale.get_weight()

            if weight_data and weight_data.get('weight') is not None:
                weight = weight_data['weight']
                # Calculate net weight with new entry weight
                net_weight = 0.0
                if self.exit_weight:
                    net_weight = abs(weight - self.exit_weight) - (self.discount or 0)

                self.write({
                    'entry_weight': weight,
                    'entry_time': fields.Datetime.now(),
                    'net_weight': net_weight,
                })

                # Create draft purchase order if supplier and product are selected
                po_message = ""
                if self.supplier_id and self.product_id:
                    try:
                        po = self._create_draft_purchase_order()
                        if po:
                            po_message = f" Draft PO {po.name} created."
                        else:
                            po_message = " PO already exists."
                    except Exception as po_error:
                        _logger.error(f"Error creating purchase order: {po_error}")
                        po_message = f" PO creation failed: {str(po_error)}"
                else:
                    po_message = " No PO created - missing supplier or product."

                # Return action to refresh the form view
                return {
                    'type': 'ir.actions.act_window',
                    'name': _('Truck Weighing'),
                    'res_model': 'truck.weighing',
                    'res_id': self.id,
                    'view_mode': 'form',
                    'target': 'current',
                    'context': {
                        'show_notification': True,
                        'notification_message': _('Entry weight captured: %s kg.%s') % (weight, po_message),
                    }
                }
            else:
                raise UserError(_('Could not read weight from scale. Please check scale connection.'))

        except Exception as e:
            _logger.error(f"Error capturing entry weight: {e}")
            raise UserError(_('Error capturing weight: %s') % str(e))

    def _create_draft_purchase_order(self):
        """Create draft purchase order with entry weight"""
        self.ensure_one()

        _logger.info(f"Creating PO for truck weighing {self.receipt_number}")

        if self.purchase_order_id:
            _logger.info(f"PO already exists: {self.purchase_order_id.name}")
            return self.purchase_order_id  # PO already created

        if not self.supplier_id or not self.product_id:
            _logger.warning(f"Missing supplier ({self.supplier_id}) or product ({self.product_id})")
            return None  # Required fields missing

        # Get default price from product
        product_price = self.product_id.standard_price or 0.0
        _logger.info(f"Product price: {product_price}, Product UOM: {self.product_id.uom_po_id.name}")

        # Create purchase order
        po_vals = {
            'partner_id': self.supplier_id.id,
            'date_order': fields.Datetime.now(),
            'origin': self.receipt_number,
            'notes': f'Created from truck weighing: {self.receipt_number}',
            'order_line': [(0, 0, {
                'product_id': self.product_id.id,
                'name': self.product_id.name,
                'product_qty': 0.0,  # Will be updated on exit
                'price_unit': product_price,
                'product_uom': self.product_id.uom_po_id.id,
                'date_planned': fields.Datetime.now(),
            })]
        }

        _logger.info(f"Creating PO with values: {po_vals}")
        purchase_order = self.env['purchase.order'].create(po_vals)
        self.purchase_order_id = purchase_order.id
        _logger.info(f"PO created successfully: {purchase_order.name}")

        return purchase_order

    def _finalize_purchase_order(self):
        """Update PO with actual quantity and confirm it"""
        self.ensure_one()

        if not self.purchase_order_id:
            return

        # Update PO line with actual quantity in tons
        po_line = self.purchase_order_id.order_line[0]
        po_line.write({
            'product_qty': self.net_weight_tons,
        })

        # Confirm purchase order
        self.purchase_order_id.button_confirm()

        # Auto-create and validate goods receipt
        for picking in self.purchase_order_id.picking_ids:
            if picking.state == 'assigned':
                # Set actual quantities
                for move_line in picking.move_line_ids:
                    move_line.qty_done = move_line.product_uom_qty

                # Validate the picking
                picking.button_validate()
    
    def action_capture_exit_weight(self):
        """Capture weight from scale for exit"""
        self.ensure_one()
        try:
            # Get the first available scale integration
            scale = self.env['scale.integration'].search([('gateway_connected', '=', True)], limit=1)
            if not scale:
                scale = self.env['scale.integration'].search([], limit=1)

            if not scale:
                raise UserError(_('No scale configuration found. Please configure scale integration first.'))

            # Get weight from scale
            weight_data = scale.get_weight()

            if weight_data and weight_data.get('weight') is not None:
                weight = weight_data['weight']
                # Calculate net weight with new exit weight
                net_weight = 0.0
                if self.entry_weight:
                    net_weight = abs(self.entry_weight - weight) - (self.discount or 0)

                self.write({
                    'exit_weight': weight,
                    'exit_time': fields.Datetime.now(),
                    'net_weight': net_weight,
                })

                # Update and confirm purchase order if exists
                po_message = ""
                if self.purchase_order_id and self.net_weight_tons > 0:
                    self._finalize_purchase_order()
                    po_message = " PO confirmed and goods received."

                # Return action to refresh the form view
                return {
                    'type': 'ir.actions.act_window',
                    'name': _('Truck Weighing'),
                    'res_model': 'truck.weighing',
                    'res_id': self.id,
                    'view_mode': 'form',
                    'target': 'current',
                    'context': {
                        'show_notification': True,
                        'notification_message': _('Exit weight captured: %s kg. Net weight: %s tons.%s') % (weight, self.net_weight_tons, po_message),
                    }
                }
            else:
                raise UserError(_('Could not read weight from scale. Please check scale connection.'))

        except Exception as e:
            _logger.error(f"Error capturing exit weight: {e}")
            raise UserError(_('Error capturing weight: %s') % str(e))
    
    def action_print_receipt(self):
        """Print thermal receipt"""
        self.ensure_one()
        return self.env.ref('truck_weighing.action_report_truck_weighing_receipt').report_action(self)
    
    def action_complete_exit(self):
        """Complete the exit process"""
        self.ensure_one()
        if not self.exit_weight:
            raise UserError(_('Exit weight must be captured before completing exit.'))

        # Calculate net weight
        net_weight = 0.0
        if self.entry_weight and self.exit_weight:
            net_weight = abs(self.entry_weight - self.exit_weight) - (self.discount or 0)

        # Update state and time
        self.write({
            'state': 'exit',
            'exit_time': fields.Datetime.now() if not self.exit_time else self.exit_time,
            'net_weight': net_weight,
        })

        # Auto-print receipt and refresh view
        report_action = self.action_print_receipt()

        # Return action to refresh the current view
        return {
            'type': 'ir.actions.client',
            'tag': 'reload',
        }

    @api.model
    def fields_get(self, allfields=None, attributes=None):
        """Override to make weight fields readonly for non-managers"""
        res = super().fields_get(allfields, attributes)

        # Check if user is not a manager
        if not self.env.user.has_group('truck_weighing.group_truck_weighing_manager'):
            # Make weight fields readonly for non-managers
            if 'entry_weight' in res:
                res['entry_weight']['readonly'] = True
            if 'exit_weight' in res:
                res['exit_weight']['readonly'] = True

        return res
    
    @api.constrains('entry_time', 'exit_time')
    def _check_times(self):
        """Validate entry and exit times"""
        for record in self:
            if record.exit_time and record.entry_time and record.exit_time < record.entry_time:
                raise ValidationError(_('Exit time cannot be before entry time.'))
    
    @api.constrains('entry_weight', 'exit_weight')
    def _check_weights(self):
        """Validate weight values"""
        for record in self:
            if record.entry_weight < 0 or record.exit_weight < 0:
                raise ValidationError(_('Weight values cannot be negative.'))



    def auto_generate_permit_numbers(self):
        """Auto-generate permit numbers if not provided"""
        self.ensure_one()
        if not self.entry_permit_number:
            self.entry_permit_number = self.env['ir.sequence'].next_by_code('truck.weighing.entry.permit') or str(self.id + 1000)
        if not self.exit_permit_number and self.state == 'exit':
            self.exit_permit_number = self.env['ir.sequence'].next_by_code('truck.weighing.exit.permit') or str(self.id + 2000)

    @api.model
    def create(self, vals):
        """Generate sequence number and permit numbers"""
        if vals.get('receipt_number', _('New')) == _('New'):
            vals['receipt_number'] = self.env['ir.sequence'].next_by_code('truck.weighing') or _('New')

        record = super().create(vals)
        record.auto_generate_permit_numbers()
        return record

    def write(self, vals):
        """Auto-generate exit permit when completing exit"""
        result = super().write(vals)
        if vals.get('state') == 'exit':
            for record in self:
                record.auto_generate_permit_numbers()
        return result
