# -*- coding: utf-8 -*-

from odoo import models, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)


class StockMoveLine(models.Model):
    _inherit = 'stock.move.line'

    def action_capture_weight(self):
        """Capture weight from scale and update quantity_done"""
        self.ensure_one()

        # Check if this is an outgoing picking
        if self.picking_id.picking_type_id.code != 'outgoing':
            raise UserError(_('Weight capture is only available for outgoing/delivery pickings.'))
        
        try:
            # Get the first available scale integration (same pattern as truck_weighing)
            scale = self.env['scale.integration'].search([('gateway_connected', '=', True)], limit=1)
            if not scale:
                scale = self.env['scale.integration'].search([], limit=1)

            if not scale:
                raise UserError(_('No scale configuration found. Please configure scale integration first.'))

            # Get weight from scale
            weight_data = scale.get_weight()
            
            if not weight_data:
                raise UserError(_('Failed to get weight from scale. Please check scale connection.'))

            captured_weight = weight_data.get('weight', 0.0)
            
            if captured_weight <= 0:
                raise UserError(_('Invalid weight reading: %.3f. Please ensure product is on scale.') % captured_weight)

            # Update the move line with captured weight
            self.quantity = captured_weight

            # Show success message
            message = _(
                'Weight captured successfully!\n\n'
                'Product: %(product)s\n'
                'Weight: %(weight).3f %(unit)s\n'
                'Status: %(status)s'
            ) % {
                'product': self.product_id.display_name,
                'weight': captured_weight,
                'unit': weight_data.get('unit', 'kg'),
                'status': 'Stable' if weight_data.get('stable') else 'Unstable',
            }

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Weight Captured'),
                    'message': message,
                    'type': 'success',
                    'sticky': False,
                }
            }

        except Exception as e:
            _logger.error(f"Error capturing weight for move line {self.id}: {e}")
            raise UserError(_('Error capturing weight: %s') % str(e))


